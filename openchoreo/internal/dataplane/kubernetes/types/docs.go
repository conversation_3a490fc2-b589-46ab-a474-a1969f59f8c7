// Copyright 2025 The OpenChoreo Authors
// SPDX-License-Identifier: Apache-2.0

// Package types contains resource type definitions for the Kubernetes integration that are derived from the
// following projects:
// - Cilium: https://github.com/cilium/cilium/tree/main/pkg/k8s/apis/cilium.io
// - Argo Workflow: https://github.com/argoproj/argo-workflows/tree/main/pkg/apis/workflow
// - Secret Store CSI Driver: https://github.com/kubernetes-sigs/secrets-store-csi-driver/tree/main/apis/v1
//
// The original code has been modified to fit the needs of this project.
package types
