# Contributing to OpenChoreo

Welcome! We're excited that you're interested in contributing to OpenChoreo. Whether you're fixing a bug, improving documentation, or proposing new features, every contribution makes a difference.

## Getting Started

Before you start contributing, check out our guidelines:

- **[Contribution Guide](./contribute.md)** – Learn how to setup the development environment, make changes, and submit pull requests.
- **[GitHub Workflow](./github_workflow.md)** – Understand our GitHub workflow for submitting pull requests.
- **[Release Guide](./release.md)** – Guide to releasing a new version of OpenChoreo.
- **[Resource Kind Reference Guide](./../resource-kind-reference-guide.md)** – Get details about resource kinds in OpenChoreo.

## Need Help?

If you have any questions, join our **[Discord Community](https://discord.gg/asqDFC8suT)** or open a **[GitHub Discussion](https://github.com/openchoreo/openchoreo/discussions)**.

We appreciate your contributions—thank you for making OpenChoreo better! 
