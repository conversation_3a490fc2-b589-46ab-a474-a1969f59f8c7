# Releasing a new version of OpenChoreo

The release process of OpenChoreo is tracked through a GitHub issue with a release issue template.
Please follow the steps below to create a new release issue and complete the release process.

1. Go to the [Release Issue creation template](https://github.com/openchoreo/openchoreo/issues/new?template=03-release.md)
2. Update the issue title to the desired release. Example: `Release: v1.4.0`
3. Create the issue and assign it to yourself.
4. Follow the checklist in the issue to complete the release process.

