# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: choreo.dev
layout:
- go.kubebuilder.io/v4
projectName: choreo
repo: github.com/openchoreo/openchoreo
resources:
- api:
    crdVersion: v1
  controller: true
  domain: choreo.dev
  group: core
  kind: Organization
  path: github.com/openchoreo/openchoreo/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: choreo.dev
  group: core
  kind: Project
  path: github.com/openchoreo/openchoreo/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: choreo.dev
  group: core
  kind: Build
  path: github.com/openchoreo/openchoreo/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: choreo.dev
  group: core
  kind: DeployableArtifact
  path: github.com/openchoreo/openchoreo/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: choreo.dev
  group: core
  kind: Deployment
  path: github.com/openchoreo/openchoreo/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: choreo.dev
  group: core
  kind: Endpoint
  path: github.com/openchoreo/openchoreo/api/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  domain: choreo.dev
  group: core
  kind: ConfigurationGroup
  path: github.com/openchoreo/openchoreo/api/v1
  version: v1
version: "3"
