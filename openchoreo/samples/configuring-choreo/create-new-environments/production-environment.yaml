## Sample Environment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Environment
metadata:
  name: production
  namespace: acme
  annotations:
    core.choreo.dev/display-name: Production Environment
    core.choreo.dev/description: The environment dedicated to running production-grade services and applications with high availability and reliability.
  labels:
    core.choreo.dev/organization: acme
    core.choreo.dev/name: production
spec:
  dataPlaneRef: us-east-1
  isProduction: true
  gateway:
    dnsPrefix: prod
