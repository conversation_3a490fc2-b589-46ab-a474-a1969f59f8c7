## Sample Environment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Environment
metadata:
  name: staging
  namespace: acme
  annotations:
    core.choreo.dev/display-name: Staging Environment
    core.choreo.dev/description: The staging environment for rapid integration and testing.
  labels:
    core.choreo.dev/organization: acme
    core.choreo.dev/name: staging
spec:
  dataPlaneRef: us-east-1
  isProduction: false
  gateway:
    dnsPrefix: stage
