## Sample DeploymentPipeline kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: DeploymentPipeline
metadata:
  name: pipeline-dev-stage-prod
  namespace: acme
  annotations:
    core.choreo.dev/display-name: Default Deployment Pipeline
    core.choreo.dev/description: Allows promoting from development, staging to production environments.
  labels:
    core.choreo.dev/organization: acme
    core.choreo.dev/name: pipeline-dev-stage-prod
spec:
  promotionPaths:
    - sourceEnvironmentRef: development
      targetEnvironmentRefs:
        - name: staging
          requiresApproval: false
        - name: production
          isManualApprovalRequired: true
    - sourceEnvironmentRef: staging
      targetEnvironmentRefs:
        - name: production
          requiresApproval: true
