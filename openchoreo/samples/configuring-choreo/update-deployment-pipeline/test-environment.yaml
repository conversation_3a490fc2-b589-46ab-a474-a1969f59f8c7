## Sample Environment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Environment
metadata:
  name: test
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Test Environment
    core.choreo.dev/description: The test environment for testing.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/name: test
spec:
  dataPlaneRef: us-east-1
  isProduction: false
  gateway:
    dnsPrefix: test
