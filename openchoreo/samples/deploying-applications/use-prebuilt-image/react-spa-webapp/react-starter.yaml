---
## Sample Component kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Component
metadata:
  name: react-starter-image
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: React Starter
    core.choreo.dev/description: Basic Starter project
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/name: react-starter-image
spec:
  type: WebApplication
  source:
    containerRegistry:
      imageName: choreoanonymouspullable.azurecr.io/react-spa
---
## Sample DeploymentTrack kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: DeploymentTrack
metadata:
  name: react-starter-image-main
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Main deployment track
    core.choreo.dev/description: The main deployment track for the react-starter-image component
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: react-starter-image
    core.choreo.dev/name: main
spec: {}
---
## Sample Deployable Artifact kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: DeployableArtifact
metadata:
  name: react-starter-image
  namespace: default-org
  annotations:
    core.choreo.dev/description: Deployable Artifact for react-starter-image with version v0.9
    core.choreo.dev/display-name: react-starter-image
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: react-starter-image
    core.choreo.dev/deployment-track: main
    core.choreo.dev/name: react-starter-image
spec:
  configuration:
    endpointTemplates:
      - metadata:
          name: webapp
        spec:
          service:
            basePath: /
            port: 8080
          type: HTTP
  targetArtifact:
    fromImageRef:
      tag: v0.9
---
## Sample Deployment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Deployment
metadata:
  name: react-starter-image-deployment
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: react-starter-image deployment
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/environment: development
    core.choreo.dev/component: react-starter-image
    core.choreo.dev/deployment-track: main
    core.choreo.dev/name: react-starter-image-deployment
spec:
  deploymentArtifactRef: react-starter-image
