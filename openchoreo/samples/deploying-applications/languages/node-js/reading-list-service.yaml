## Sample Component kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Component
metadata:
  name: reading-list-node-service
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Reading List Node.js Component
    core.choreo.dev/description: A simple REST API service for managing a reading list, built with Node.js.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/name: reading-list-node-service
spec:
  type: Service
  source:
    gitRepository:
      url: https://github.com/wso2/choreo-samples
      authentication:
        secretRef: choreo-bot-git-pat
---

## Sample DeploymentTrack kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: DeploymentTrack
metadata:
  name: reading-list-node-service-main
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Main Deployment Track
    core.choreo.dev/description: Deployment track for the Reading List Node.js service.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: reading-list-node-service
    core.choreo.dev/name: reading-list-node-service-main
spec:
  buildTemplateSpec:
    branch: main
    path: /reading-books-list-service-nodejs
    buildConfiguration:
      buildpack:
        name: Node.js
---

## Sample Build kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Build
metadata:
  name: reading-list-node-service-build-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Default Build
    core.choreo.dev/description: Initial build configuration for the Reading List Node.js service.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: reading-list-node-service
    core.choreo.dev/deployment-track: reading-list-node-service-main
    core.choreo.dev/name: reading-list-node-service-build-01
spec:
  branch: main
  path: /reading-books-list-service-nodejs
  buildConfiguration:
    buildpack:
      name: Node.js
      version: 18.x.x
---

#### Sample Deployment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Deployment
metadata:
  name: reading-list-node-service-development-deployment-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Reading List Node.js Service Deployment
    core.choreo.dev/description: Initial deployment configuration for the Reading List Node.js service.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/environment: development
    core.choreo.dev/component: reading-list-node-service
    core.choreo.dev/deployment-track: reading-list-node-service-main
    core.choreo.dev/name: reading-list-node-service-development-deployment-01
spec:
  deploymentArtifactRef: reading-list-node-service-build-01
