## Component kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Component
metadata:
  name: hello-world-web-application-php
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Hello World PHP Web Application
    core.choreo.dev/description: A simple web application built with PHP.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/name: hello-world-web-application-php
spec:
  type: WebApplication
  source:
    gitRepository:
      url: https://github.com/wso2/choreo-samples
      authentication:
        secretRef: choreo-bot-git-pat
---

## DeploymentTrack kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: DeploymentTrack
metadata:
  name: hello-world-web-application-php-main
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Main Deployment Track
    core.choreo.dev/description: Deployment track for the Hello World PHP web application.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: hello-world-web-application-php
    core.choreo.dev/name: hello-world-web-application-php-main
spec:
  buildTemplateSpec:
    branch: main
    path: /hello-world-php-webapp
    buildConfiguration:
      buildpack:
        name: PHP
---

## Build kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Build
metadata:
  name: hello-world-web-application-php-build-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Default Build
    core.choreo.dev/description: Initial build configuration for the Hello World PHP web application.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: hello-world-web-application-php
    core.choreo.dev/deployment-track: hello-world-web-application-php-main
    core.choreo.dev/name: hello-world-web-application-php-build-01
spec:
  branch: main
  path: /hello-world-php-webapp
  buildConfiguration:
    buildpack:
      name: PHP
      version: 8.1.x
---
#
### Deployment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Deployment
metadata:
  name: hello-world-web-application-php-development-deployment-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Hello World PHP Web Application Deployment
    core.choreo.dev/description: Initial deployment configuration for the Hello World PHP web application.
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/environment: development
    core.choreo.dev/component: hello-world-web-application-php
    core.choreo.dev/deployment-track: hello-world-web-application-php-main
    core.choreo.dev/name: hello-world-web-application-php-development-deployment-01
spec:
  deploymentArtifactRef: hello-world-web-application-php-build-01
