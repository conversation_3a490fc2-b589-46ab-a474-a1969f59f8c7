## Sample Component kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Component
metadata:
  name: patient-management-service
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Patient Management Ballerina Component
    core.choreo.dev/description: This component can send manage patients through its REST API
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/name: patient-management-service
spec:
  type: Service
  source:
    gitRepository:
      url: https://github.com/wso2/choreo-samples
      authentication:
        secretRef: choreo-bot-git-pat
---

## Sample DeploymentTrack kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: DeploymentTrack
metadata:
  name: patient-management-service-main
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Deployment Track
    core.choreo.dev/description: Main deployment track
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: patient-management-service
    core.choreo.dev/name: patient-management-service-main
spec:
  buildTemplateSpec:
    branch: main
    path: /patient-management-service
    buildConfiguration:
      buildpack:
        name: Ballerina
---

## Sample Build kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Build
metadata:
  name: patient-management-service-build-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Default Build
    core.choreo.dev/description: Initial build configuration
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: patient-management-service
    core.choreo.dev/deployment-track: patient-management-service-main
    core.choreo.dev/name: patient-management-service-build-01
spec:
  branch: main
  path: /patient-management-service
  buildConfiguration:
    buildpack:
      name: Ballerina
      version: 2201.10.4
---

#### Sample Deployment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Deployment
metadata:
  name: patient-management-service-development-deployment-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Deployment of Patient Management App
    core.choreo.dev/description: Initial deployment configuration
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/environment: development
    core.choreo.dev/component: patient-management-service
    core.choreo.dev/deployment-track: patient-management-service-main
    core.choreo.dev/name: patient-management-service-development-deployment-01
spec:
  deploymentArtifactRef: patient-management-service-build-01
