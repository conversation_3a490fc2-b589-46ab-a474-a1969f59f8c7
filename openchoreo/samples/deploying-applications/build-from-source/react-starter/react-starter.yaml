## Sample Component kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Component
metadata:
  name: react-starter
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: React Starter
    core.choreo.dev/description: Basic Starter project
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/name: react-starter
spec:
  type: WebApplication
  source:
    gitRepository:
      url: https://github.com/docker/awesome-compose
      authentication:
        secretRef: choreo-bot-git-pat
---
## Sample DeploymentTrack kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: DeploymentTrack
metadata:
  name: react-starter-main
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Main deployment track
    core.choreo.dev/description: The main deployment track for the react-starter component
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: react-starter
    core.choreo.dev/name: main
spec:
  buildTemplateSpec:
    branch: master
    path: /react-nginx
    buildConfiguration:
      docker:
        context: /react-nginx
        dockerfilePath: /react-nginx/Dockerfile
---
## Sample Build kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Build
metadata:
  name: react-starter-build-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Build for react-starter
    core.choreo.dev/description: Build 1 triggered for react-starter
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: react-starter
    core.choreo.dev/deployment-track: main
    core.choreo.dev/name: react-starter-build
spec:
  branch: master
  path: /react-nginx
  buildConfiguration:
    docker:
      context: /react-nginx
      dockerfilePath: /react-nginx/Dockerfile
---
## Sample Deployment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Deployment
metadata:
  name: react-starter-deployment
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: react-starter deployment
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/environment: development
    core.choreo.dev/component: react-starter
    core.choreo.dev/deployment-track: main
    core.choreo.dev/name: react-starter-deployment
spec:
  deploymentArtifactRef: react-starter-build-01
