## Sample Component kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Component
metadata:
  name: time-logger
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Time Logger Component
    core.choreo.dev/description: This component logs the time periodically
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/name: time-logger
spec:
  type: ScheduledTask
  source:
    gitRepository:
      url: https://github.com/wso2/choreo-samples
      authentication:
        secretRef: choreo-bot-git-pat
---

## Sample DeploymentTrack kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: DeploymentTrack
metadata:
  name: time-logger-main
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Deployment Track
    core.choreo.dev/description: Main deployment track
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: time-logger
    core.choreo.dev/name: time-logger-main
spec:
  buildTemplateSpec:
    branch: main
    path: /docker-time-logger-schedule
    buildConfiguration:
      buildpack:
        name: Go
---

## Sample Build kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Build
metadata:
  name: time-logger-build-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Default Build
    core.choreo.dev/description: Initial build configuration
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/component: time-logger
    core.choreo.dev/deployment-track: time-logger-main
    core.choreo.dev/name: time-logger-build-01
spec:
  branch: main
  path: /docker-time-logger-schedule
  buildConfiguration:
    buildpack:
      name: Go
      version: 1.x
---

#### Sample Deployment kind (Namespaced)
apiVersion: core.choreo.dev/v1
kind: Deployment
metadata:
  name: time-logger-development-deployment-01
  namespace: default-org
  annotations:
    core.choreo.dev/display-name: Deployment of Time Logger
    core.choreo.dev/description: Initial deployment configuration
  labels:
    core.choreo.dev/organization: default-org
    core.choreo.dev/project: default-project
    core.choreo.dev/environment: development
    core.choreo.dev/component: time-logger
    core.choreo.dev/deployment-track: time-logger-main
    core.choreo.dev/name: time-logger-development-deployment
spec:
  deploymentArtifactRef: time-logger-build-01
