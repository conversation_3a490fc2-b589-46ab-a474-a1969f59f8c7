apiVersion: core.choreo.dev/v1
kind: DeployableArtifact
metadata:
  name: github-issue-reporter-daily-deployable-artifact-2024-12-10-1
  namespace: default-organization
  annotations:
    core.choreo.dev/display-name: Daily Deployable Artifact
    core.choreo.dev/description: Generated artifact from Build 2024-12-10 1
  labels:
    core.choreo.dev/organization: default-organization
    core.choreo.dev/project: internal-apps
    core.choreo.dev/component: github-issue-reporter
    core.choreo.dev/deployment-track: daily
    core.choreo.dev/name: build-2024-12-10-1
spec:
  targetArtifact:
    fromBuildRef:
      name: build-2024-12-10-1
  configuration:
    application:
      env:
        - key: GITHUB_REPOSITORY
          value: https://github.com/wso2/product-apim
      task:
        disabled: false
        schedule:
          cron: "*/1 * * * *"
          timezone: Asia/Colombo
