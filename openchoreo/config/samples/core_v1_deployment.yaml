apiVersion: core.choreo.dev/v1
kind: Deployment
metadata:
  name: github-issue-reporter-daily-deployment-2024-12-10-1-development
  namespace: default-organization
  annotations:
    core.choreo.dev/display-name: Daily Deployment
    core.choreo.dev/description: This deployment will produce a daily report
  labels:
    core.choreo.dev/organization: default-organization
    core.choreo.dev/project: internal-apps
    core.choreo.dev/environment: development
    core.choreo.dev/component: github-issue-reporter
    core.choreo.dev/deployment-track: daily
    core.choreo.dev/name: development
spec:
  deploymentArtifactRef: github-issue-reporter-daily-deployable-artifact-2024-12-10-1

