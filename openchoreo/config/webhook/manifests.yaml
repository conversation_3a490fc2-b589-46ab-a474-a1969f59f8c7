---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: mutating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /mutate-core-choreo-dev-v1-project
  failurePolicy: Fail
  name: mproject-v1.kb.io
  rules:
  - apiGroups:
    - core.choreo.dev
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - projects
  sideEffects: None
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-core-choreo-dev-v1-project
  failurePolicy: Fail
  name: vproject-v1.kb.io
  rules:
  - apiGroups:
    - core.choreo.dev
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - projects
  sideEffects: None
