resources:
  # All RBAC will be applied under this service account in
  # the deployment namespace. You may comment out this resource
  # if your manager will use a service account that exists at
  # runtime. Be sure to update RoleBinding and ClusterRoleBinding
  # subjects if changing service account names.
  - service_account.yaml
  - role.yaml
  - role_binding.yaml
  - leader_election_role.yaml
  - leader_election_role_binding.yaml
  # The following RBAC configurations are used to protect
  # the metrics endpoint with authn/authz. These configurations
  # ensure that only authorized users and service accounts
  # can access the metrics endpoint. Comment the following
  # permissions if you want to disable this protection.
  # More info: https://book.kubebuilder.io/reference/metrics.html
  - metrics_auth_role.yaml
  - metrics_auth_role_binding.yaml
  - metrics_reader_role.yaml
  # For each CRD, "Editor" and "Viewer" roles are scaffolded by
  # default, aiding admins in cluster management. Those roles are
  # not used by the Project itself. You can comment the following lines
  # if you do not want those helpers be installed with your Project.
  - build_editor_role.yaml
  - build_viewer_role.yaml
  - project_editor_role.yaml
  - project_viewer_role.yaml
  - organization_editor_role.yaml
  - organization_viewer_role.yaml
  # For each CRD, "Editor" and "Viewer" roles are scaffolded by
  # default, aiding admins in cluster management. Those roles are
  # not used by the Project itself. You can comment the following lines
  # if you do not want those helpers be installed with your Project.
  - deploymenttrack_editor_role.yaml
  - deploymenttrack_viewer_role.yaml
  - component_editor_role.yaml
  - component_viewer_role.yaml
  - deploymentpipeline_editor_role.yaml
  - deploymentpipeline_viewer_role.yaml
  - dataplane_editor_role.yaml
  - dataplane_viewer_role.yaml
  - environment_editor_role.yaml
  - environment_viewer_role.yaml
  - deployment_editor_role.yaml
  - deployment_viewer_role.yaml
  - deployableartifact_editor_role.yaml
  - deployableartifact_viewer_role.yaml
# For each CRD, "Admin", "Editor" and "Viewer" roles are scaffolded by
# default, aiding admins in cluster management. Those roles are
# not used by the {{ .ProjectName }} itself. You can comment the following lines
# if you do not want those helpers be installed with your Project.
  - endpoint_admin_role.yaml
  - endpoint_editor_role.yaml
  - endpoint_viewer_role.yaml

# For each CRD, "Editor" and "Viewer" roles are scaffolded by
# default, aiding admins in cluster management. Those roles are
# not used by the Project itself. You can comment the following lines
# if you do not want those helpers be installed with your Project.
  - configurationgroup_editor_role.yaml
  - configurationgroup_viewer_role.yaml
