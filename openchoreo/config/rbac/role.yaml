---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - namespaces
  - serviceaccounts
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - argoproj.io
  resources:
  - workflows
  - workflowtaskresults
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - batch
  resources:
  - cronjobs
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - cilium.io
  resources:
  - ciliumnetworkpolicies
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - builds
  - components
  - dataplanes
  - deployableartifacts
  - deploymentpipelines
  - deployments
  - deploymenttracks
  - endpoints
  - environments
  - organizations
  - projects
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - builds/finalizers
  - components/finalizers
  - dataplanes/finalizers
  - deployableartifacts/finalizers
  - deploymentpipelines/finalizers
  - deployments/finalizers
  - deploymenttracks/finalizers
  - endpoints/finalizers
  - environments/finalizers
  - organizations/finalizers
  - projects/finalizers
  verbs:
  - update
- apiGroups:
  - core.choreo.dev
  resources:
  - builds/status
  - components/status
  - dataplanes/status
  - deployableartifacts/status
  - deploymentpipelines/status
  - deployments/status
  - deploymenttracks/status
  - endpoints/status
  - environments/status
  - organizations/status
  - projects/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - core.choreo.dev
  resources:
  - configurationgroups
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.envoyproxy.io
  resources:
  - securitypolicies
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - httproutes
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - rbac.authorization.k8s.io
  resources:
  - rolebindings
  - roles
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - secrets-store.csi.x-k8s.io
  resources:
  - secretproviderclasses
  verbs:
  - create
  - delete
  - deletecollection
  - get
  - list
  - patch
  - update
  - watch
