# permissions for end users to view builds.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: build-viewer-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - builds
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - builds/status
  verbs:
  - get
