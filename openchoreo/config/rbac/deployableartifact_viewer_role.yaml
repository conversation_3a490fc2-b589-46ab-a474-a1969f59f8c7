# permissions for end users to view deployableartifacts.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: deployableartifact-viewer-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - deployableartifacts
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - deployableartifacts/status
  verbs:
  - get
