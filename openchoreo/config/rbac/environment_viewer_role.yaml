# permissions for end users to view environments.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: environment-viewer-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - environments
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - environments/status
  verbs:
  - get
