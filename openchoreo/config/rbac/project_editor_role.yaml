# permissions for end users to edit projects.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: project-editor-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - projects
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - projects/status
  verbs:
  - get
