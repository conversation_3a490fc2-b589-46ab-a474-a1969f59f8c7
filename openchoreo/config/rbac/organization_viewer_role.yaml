# permissions for end users to view organizations.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: organization-viewer-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - organizations
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - organizations/status
  verbs:
  - get
