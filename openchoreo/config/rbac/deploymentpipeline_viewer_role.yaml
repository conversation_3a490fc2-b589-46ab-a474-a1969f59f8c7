# permissions for end users to view deploymentpipelines.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: deploymentpipeline-viewer-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - deploymentpipelines
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - deploymentpipelines/status
  verbs:
  - get
