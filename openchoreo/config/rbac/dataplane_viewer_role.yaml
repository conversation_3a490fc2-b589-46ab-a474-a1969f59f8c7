# permissions for end users to view dataplanes.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: dataplane-viewer-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - dataplanes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - dataplanes/status
  verbs:
  - get
