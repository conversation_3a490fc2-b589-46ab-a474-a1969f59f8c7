# permissions for end users to edit deploymentpipelines.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: deploymentpipeline-editor-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - deploymentpipelines
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - deploymentpipelines/status
  verbs:
  - get
