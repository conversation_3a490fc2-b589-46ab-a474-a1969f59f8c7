# permissions for end users to view configurationgroups.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: configurationgroup-viewer-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - configurationgroups
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - configurationgroups/status
  verbs:
  - get
