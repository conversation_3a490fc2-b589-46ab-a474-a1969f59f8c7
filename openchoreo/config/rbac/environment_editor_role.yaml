# permissions for end users to edit environments.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: environment-editor-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - environments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - environments/status
  verbs:
  - get
