# permissions for end users to view deploymenttracks.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: choreo
    app.kubernetes.io/managed-by: kustomize
  name: deploymenttrack-viewer-role
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - deploymenttracks
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - deploymenttracks/status
  verbs:
  - get
