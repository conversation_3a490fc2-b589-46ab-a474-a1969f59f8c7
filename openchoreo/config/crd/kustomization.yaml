# This kustomization.yaml is not intended to be run by itself,
# since it depends on service name and namespace that are out of this kustomize package.
# It should be run by config/default
resources:
  - bases/core.choreo.dev_organizations.yaml
  - bases/core.choreo.dev_builds.yaml
  - bases/core.choreo.dev_projects.yaml
  - bases/core.choreo.dev_environments.yaml
  - bases/core.choreo.dev_dataplanes.yaml
  - bases/core.choreo.dev_deploymentpipelines.yaml
  - bases/core.choreo.dev_components.yaml
  - bases/core.choreo.dev_deploymenttracks.yaml
  - bases/core.choreo.dev_deployableartifacts.yaml
  - bases/core.choreo.dev_deployments.yaml
  - bases/core.choreo.dev_endpoints.yaml
  - bases/core.choreo.dev_configurationgroups.yaml
# +kubebuilder:scaffold:crdkustomizeresource

# patches:
# [WEBHOOK] To enable webhook, uncomment all the sections with [WEBHOOK] prefix.
# patches here are for enabling the conversion webhook for each CRD
# +kubebuilder:scaffold:crdkustomizewebhookpatch

# [CERTMANAGER] To enable cert-manager, uncomment all the sections with [CERTMANAGER] prefix.
# patches here are for enabling the CA injection for each CRD
# +kubebuilder:scaffold:crdkustomizecainjectionpatch

# [WEBHOOK] To enable webhook, uncomment the following section
# the following config is for teaching kustomize how to do kustomization for CRDs.
#configurations:
#- kustomizeconfig.yaml
