---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: builds.core.choreo.dev
spec:
  group: core.choreo.dev
  names:
    kind: Build
    listKind: BuildList
    plural: builds
    singular: build
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: Build is the Schema for the builds API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: BuildSpec defines the desired state of Build.
            properties:
              autoBuild:
                type: boolean
              branch:
                type: string
              buildConfiguration:
                description: BuildConfiguration specifies the build configuration
                  details
                properties:
                  buildpack:
                    description: Buildpack specifies the buildpack to use
                    properties:
                      name:
                        type: string
                      version:
                        type: string
                    required:
                    - name
                    type: object
                  docker:
                    description: Docker specifies the Docker-specific build configuration
                    properties:
                      context:
                        description: Context specifies the build context path
                        type: string
                      dockerfilePath:
                        description: DockerfilePath specifies the path to the Dockerfile
                        type: string
                    required:
                    - context
                    - dockerfilePath
                    type: object
                type: object
              buildEnvironment:
                properties:
                  env:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                      required:
                      - name
                      - value
                      type: object
                    type: array
                  envFrom:
                    items:
                      properties:
                        secretRef:
                          type: string
                      required:
                      - secretRef
                      type: object
                    type: array
                type: object
              gitRevision:
                type: string
              path:
                type: string
            required:
            - buildConfiguration
            type: object
          status:
            description: BuildStatus defines the observed state of Build.
            properties:
              conditions:
                description: Conditions represent the latest available observations
                  of an object's current state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              imageStatus:
                properties:
                  image:
                    type: string
                required:
                - image
                type: object
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
