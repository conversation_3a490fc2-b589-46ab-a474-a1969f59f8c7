# This makefile contains all the make targets related to Helm charts.

HELM_CHARTS_DIR := $(PROJECT_DIR)/install/helm
HELM_CHARTS := $(wildcard $(HELM_CHARTS_DIR)/*)
HELM_CHART_NAMES := $(foreach c,$(HELM_CHARTS),$(notdir $(c)))
HELM_CHART_VERSION ?= 0.0.0-latest-dev

HELM_CHARTS_OUTPUT_DIR := $(PROJECT_BIN_DIR)/dist/charts
HELM_OCI_REGISTRY ?= oci://ghcr.io/openchoreo/helm-charts

# Define the controller image that is used in the Choreo helm chart.
# This value should be equal to the controller image define in `DOCKER_BUILD_IMAGES` in docker.mk
HELM_CONTROLLER_IMAGE := $(IMAGE_REPO_PREFIX)/controller
HELM_CONTROLLER_IMAGE_PULL_POLICY ?= Always

##@ Helm

# Define the generation targets for the helm charts that are required for the helm package and push.
# Ex: make helm-generate.cilium, make helm-generate.choreo
.PHONY: helm-generate.%
helm-generate.%: ## Generate helm chart for the specified chart name.
	@if [ -z "$(filter $*,$(HELM_CHART_NAMES))" ]; then \
    		$(call log_error, Invalid helm generate target '$*'); \
    		exit 1; \
	fi
	$(eval CHART_NAME := $(word 1,$(subst ., ,$*)))
	$(eval CHART_PATH := $(HELM_CHARTS_DIR)/$(CHART_NAME))
	@$(call log_info, Generating helm chart '$(CHART_NAME)')
	@# Run helmify against the Choreo chart.
	@# This will add new line at the end of each file that is generated by helmify and,
	@# append choreo.values.yaml (excluding comments) to values.yaml
	@# TODO: Update to use kubebuilder helm plugin instead of helmify
	@if [ ${CHART_NAME} == "choreo-control-plane" ]; then \
		$(MAKE) manifests kustomize helmify yq; \
		$(KUSTOMIZE) build config/default | $(HELMIFY) -v -crd-dir -cert-manager-as-subchart $(CHART_PATH); \
		TARGET_DIR=$(CHART_PATH); \
		find "$$TARGET_DIR" -type f -name "*.yaml" | while read -r file; do \
		  if [ -n "$$(tail -c 1 "$$file")" ]; then \
			echo >> "$$file"; \
		  fi; \
		done; \
		VALUES_FILE=$(CHART_PATH)/values.yaml; \
		CHOREO_VALUES=$(CHART_PATH)/choreo.values.yaml; \
		if [ -f "$$VALUES_FILE" ]; then \
		  $(YQ) eval '.controllerManager.manager.image.repository = "$(HELM_CONTROLLER_IMAGE)"' -i $$VALUES_FILE; \
		  $(YQ) eval '.controllerManager.manager.image.tag = "$(TAG)"' -i $$VALUES_FILE; \
		  $(YQ) eval '.controllerManager.manager.imagePullPolicy = "$(HELM_CONTROLLER_IMAGE_PULL_POLICY)"' -i $$VALUES_FILE; \
		  HEADER="# =======================================================\n# DO NOT MODIFY THIS FILE - THIS IS A GENERATED FILE\n# Instead, make your changes in choreo.values.yaml\n# =======================================================\n"; \
		  echo -e "$$HEADER" > "$$VALUES_FILE.tmp"; \
		  cat "$$VALUES_FILE" >> "$$VALUES_FILE.tmp"; \
		  mv "$$VALUES_FILE.tmp" "$$VALUES_FILE"; \
		fi; \
		if [ -f "$$CHOREO_VALUES" ] && [ -f "$$VALUES_FILE" ]; then \
		  grep -v '^\s*#' "$$CHOREO_VALUES" >> "$$VALUES_FILE"; \
		fi \
	fi
	helm dependency update $(CHART_PATH)
	helm lint $(CHART_PATH)

.PHONY: helm-generate
helm-generate: $(addprefix helm-generate., $(HELM_CHART_NAMES)) ## Generate all helm charts.


.PHONY: helm-package.%
helm-package.%: helm-generate.% ## Package helm chart for the specified chart name.
	@if [ -z "$(filter $*,$(HELM_CHART_NAMES))" ]; then \
    		$(call log_error, Invalid helm package target '$*'); \
    		exit 1; \
	fi
	$(eval CHART_NAME := $(word 1,$(subst ., ,$*)))
	$(eval CHART_PATH := $(HELM_CHARTS_DIR)/$(CHART_NAME))
	helm package $(CHART_PATH) --app-version ${TAG} --version ${HELM_CHART_VERSION} --destination $(HELM_CHARTS_OUTPUT_DIR)

.PHONY: helm-package
helm-package: $(addprefix helm-package., $(HELM_CHART_NAMES)) ## Package all helm charts.

.PHONY: helm-push.%
helm-push.%: helm-package.% ## Push helm chart for the specified chart name.
	@if [ -z "$(filter $*,$(HELM_CHART_NAMES))" ]; then \
    		$(call log_error, Invalid helm package target '$*'); \
    		exit 1; \
	fi
	$(eval CHART_NAME := $(word 1,$(subst ., ,$*)))
	helm push $(HELM_CHARTS_OUTPUT_DIR)/$(CHART_NAME)-$(HELM_CHART_VERSION).tgz $(HELM_OCI_REGISTRY)

.PHONY: helm-push
helm-push: $(addprefix helm-push., $(HELM_CHART_NAMES)) ## Push all helm charts.
