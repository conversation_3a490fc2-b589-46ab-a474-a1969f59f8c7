# =======================================================
# DO NOT MODIFY THIS FILE - THIS IS A GENERATED FILE
# Instead, make your changes in choreo.values.yaml
# =======================================================

certmanager:
  enabled: true
  installCRDs: true
controllerManager:
  manager:
    args:
      - --metrics-bind-address=:8443
      - --leader-elect
      - --health-probe-bind-address=:8081
    containerSecurityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
          - ALL
    image:
      repository: ghcr.io/openchoreo/controller
      tag: latest-dev
    imagePullPolicy: Always
    resources:
      limits:
        cpu: 500m
        memory: 128Mi
      requests:
        cpu: 10m
        memory: 64Mi
  podSecurityContext:
    runAsNonRoot: true
  replicas: 1
  serviceAccount:
    annotations: {}
kubernetesClusterDomain: cluster.local
metricsService:
  ports:
    - name: https
      port: 8443
      protocol: TCP
      targetPort: 8443
  type: ClusterIP
webhookService:
  ports:
    - port: 443
      protocol: TCP
      targetPort: 9443
  type: ClusterIP
choreoDefaultValues:
  enableDefaultOrgAndProject: true
  organization:
    enabled: true
    name: default-org
    displayName: Default Organization
    description: Getting started with your first organization
  dataPlane:
    name: default-dataplane
    namespace: default-org
    organization: default-org
  Environments:
    - name: development
      displayName: Development
      description: Development environment for testing
      namespace: default-org
      organization: default-org
      dnsPrefix: dev
      isCritical: false
    - name: staging
      displayName: Staging
      description: Staging environment for pre-production testing
      namespace: default-org
      organization: default-org
      dnsPrefix: staging
      isCritical: false
    - name: production
      displayName: Production
      description: Production environment
      namespace: default-org
      organization: default-org
      dnsPrefix: prod
      isCritical: true
  deploymentPipeline:
    name: default-pipeline
    namespace: default-org
    organization: default-org
    displayName: Default Pipeline
    description: Standard deployment pipeline with dev, staging, and prod environments
    promotionOrder:
      - sourceEnvironmentRef: development
        targetEnvironmentRefs:
          - name: staging
            requiresApproval: false
          - name: production
            isManualApprovalRequired: true
      - sourceEnvironmentRef: staging
        targetEnvironmentRefs:
            - name: production
              requiresApproval: true
  DefaultProject:
    name: default-project
    namespace: default-org
    organization: default-org
    displayName: Default Project
    description: Your first project to get started
waitJob:
  image: bitnami/kubectl:latest
metricsServer:
  enabled: false
  kubeletInsecureTlsEnabled: true
certmanager:
  enabled: true
  crds:
    enabled: true
  resources:
    requests:
      cpu: 10m
      memory: 32Mi
    limits:
      cpu: 50m
      memory: 64Mi
  cainjector:
    resources:
      requests:
        cpu: 10m
        memory: 32Mi
      limits:
        cpu: 50m
        memory: 64Mi
  webhook:
    resources:
      requests:
        cpu: 10m
        memory: 32Mi
      limits:
        cpu: 50m
        memory: 64Mi
