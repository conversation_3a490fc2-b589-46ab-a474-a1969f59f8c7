apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    cert-manager.io/inject-ca-from: choreo-system/choreo-serving-cert
    controller-gen.kubebuilder.io/version: v0.16.4
  name: deployableartifacts.core.choreo.dev
spec:
  group: core.choreo.dev
  names:
    kind: DeployableArtifact
    listKind: DeployableArtifactList
    plural: deployableartifacts
    singular: deployableartifact
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: DeployableArtifact is the Schema for the deployableartifacts
          API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: DeployableArtifactSpec defines the desired state of DeployableArtifact.
            properties:
              configuration:
                description: Configuration parameters for this deployable artifact.
                properties:
                  application:
                    description: Application runtime parameters/configurations.
                    properties:
                      args:
                        description: Command line arguments to pass.
                        items:
                          type: string
                        type: array
                      env:
                        description: Explicit environment variables.
                        items:
                          description: EnvVar represents an environment variable present
                            in the container.
                          properties:
                            key:
                              description: The environment variable key.
                              type: string
                            value:
                              description: |-
                                The literal value of the environment variable.
                                Mutually exclusive with valueFrom.
                              type: string
                            valueFrom:
                              description: |-
                                Extract the environment variable value from another resource.
                                Mutually exclusive with value.
                              properties:
                                configurationGroupRef:
                                  description: Reference to a configuration group.
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      type: string
                                  required:
                                  - key
                                  - name
                                  type: object
                                secretRef:
                                  description: Reference to a secret resource.
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      type: string
                                  required:
                                  - key
                                  - name
                                  type: object
                              type: object
                          required:
                          - key
                          type: object
                        type: array
                      envFrom:
                        description: Bulk import environment variables from references.
                        items:
                          description: EnvFromSource allows importing all environment
                            variables from a source.
                          properties:
                            configurationGroupRef:
                              description: Reference to a configuration group (entire
                                group).
                              properties:
                                name:
                                  type: string
                              required:
                              - name
                              type: object
                            secretRef:
                              description: Reference to a secret resource (entire
                                secret).
                              properties:
                                name:
                                  type: string
                              required:
                              - name
                              type: object
                          type: object
                        type: array
                      fileMounts:
                        description: Single-file mounts.
                        items:
                          description: FileMount represents one file mounted from
                            data/inline content.
                          properties:
                            mountPath:
                              type: string
                            value:
                              description: |-
                                Inline file content.
                                Mutually exclusive with valueFrom.
                              type: string
                            valueFrom:
                              description: References to an external data source for
                                the file content.
                              properties:
                                configurationGroupRef:
                                  description: ConfigurationGroupKeyRef references
                                    a specific key in a configuration group.
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      type: string
                                  required:
                                  - key
                                  - name
                                  type: object
                                secretRef:
                                  description: SecretKeyRef references a specific
                                    key in a K8s secret.
                                  properties:
                                    key:
                                      type: string
                                    name:
                                      type: string
                                  required:
                                  - key
                                  - name
                                  type: object
                              type: object
                          required:
                          - mountPath
                          type: object
                        type: array
                      fileMountsFrom:
                        description: Bulk import file mounts from references.
                        items:
                          description: FileMountsFromSource allows importing multiple
                            files from a source.
                          properties:
                            configurationGroupRef:
                              description: ConfigurationGroupMountRef references a
                                config group as files in a directory.
                              properties:
                                mountPath:
                                  description: Absolute directory path to mount the
                                    config group contents.
                                  type: string
                                name:
                                  type: string
                              required:
                              - mountPath
                              - name
                              type: object
                            secretRef:
                              description: SecretMountRef references a secret resource
                                as files in a directory.
                              properties:
                                mountPath:
                                  description: Absolute directory path to mount the
                                    secret contents.
                                  type: string
                                name:
                                  type: string
                              required:
                              - mountPath
                              - name
                              type: object
                          type: object
                        type: array
                      probes:
                        description: Probes (readiness/liveness) to monitor the container.
                        properties:
                          livenessProbe:
                            description: |-
                              Probe describes a health check to be performed against a container to determine whether it is
                              alive or ready to receive traffic.
                            properties:
                              exec:
                                description: Exec specifies a command to execute in
                                  the container.
                                properties:
                                  command:
                                    description: |-
                                      Command is the command line to execute inside the container, the working directory for the
                                      command  is root ('/') in the container's filesystem. The command is simply exec'd, it is
                                      not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use
                                      a shell, you need to explicitly call out to that shell.
                                      Exit status of 0 is treated as live/healthy and non-zero is unhealthy.
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                type: object
                              failureThreshold:
                                description: |-
                                  Minimum consecutive failures for the probe to be considered failed after having succeeded.
                                  Defaults to 3. Minimum value is 1.
                                format: int32
                                type: integer
                              grpc:
                                description: GRPC specifies a GRPC HealthCheckRequest.
                                properties:
                                  port:
                                    description: Port number of the gRPC service.
                                      Number must be in the range 1 to 65535.
                                    format: int32
                                    type: integer
                                  service:
                                    default: ""
                                    description: |-
                                      Service is the name of the service to place in the gRPC HealthCheckRequest
                                      (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).

                                      If this is not specified, the default behavior is defined by gRPC.
                                    type: string
                                required:
                                - port
                                type: object
                              httpGet:
                                description: HTTPGet specifies an HTTP GET request
                                  to perform.
                                properties:
                                  host:
                                    description: |-
                                      Host name to connect to, defaults to the pod IP. You probably want to set
                                      "Host" in httpHeaders instead.
                                    type: string
                                  httpHeaders:
                                    description: Custom headers to set in the request.
                                      HTTP allows repeated headers.
                                    items:
                                      description: HTTPHeader describes a custom header
                                        to be used in HTTP probes
                                      properties:
                                        name:
                                          description: |-
                                            The header field name.
                                            This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                          type: string
                                        value:
                                          description: The header field value
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  path:
                                    description: Path to access on the HTTP server.
                                    type: string
                                  port:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: |-
                                      Name or number of the port to access on the container.
                                      Number must be in the range 1 to 65535.
                                      Name must be an IANA_SVC_NAME.
                                    x-kubernetes-int-or-string: true
                                  scheme:
                                    description: |-
                                      Scheme to use for connecting to the host.
                                      Defaults to HTTP.
                                    type: string
                                required:
                                - port
                                type: object
                              initialDelaySeconds:
                                description: |-
                                  Number of seconds after the container has started before liveness probes are initiated.
                                  More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                                format: int32
                                type: integer
                              periodSeconds:
                                description: |-
                                  How often (in seconds) to perform the probe.
                                  Default to 10 seconds. Minimum value is 1.
                                format: int32
                                type: integer
                              successThreshold:
                                description: |-
                                  Minimum consecutive successes for the probe to be considered successful after having failed.
                                  Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                                format: int32
                                type: integer
                              tcpSocket:
                                description: TCPSocket specifies a connection to a
                                  TCP port.
                                properties:
                                  host:
                                    description: 'Optional: Host name to connect to,
                                      defaults to the pod IP.'
                                    type: string
                                  port:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: |-
                                      Number or name of the port to access on the container.
                                      Number must be in the range 1 to 65535.
                                      Name must be an IANA_SVC_NAME.
                                    x-kubernetes-int-or-string: true
                                required:
                                - port
                                type: object
                              terminationGracePeriodSeconds:
                                description: |-
                                  Optional duration in seconds the pod needs to terminate gracefully upon probe failure.
                                  The grace period is the duration in seconds after the processes running in the pod are sent
                                  a termination signal and the time when the processes are forcibly halted with a kill signal.
                                  Set this value longer than the expected cleanup time for your process.
                                  If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this
                                  value overrides the value provided by the pod spec.
                                  Value must be non-negative integer. The value zero indicates stop immediately via
                                  the kill signal (no opportunity to shut down).
                                  This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.
                                  Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.
                                format: int64
                                type: integer
                              timeoutSeconds:
                                description: |-
                                  Number of seconds after which the probe times out.
                                  Defaults to 1 second. Minimum value is 1.
                                  More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                                format: int32
                                type: integer
                            type: object
                          readinessProbe:
                            description: |-
                              Probe describes a health check to be performed against a container to determine whether it is
                              alive or ready to receive traffic.
                            properties:
                              exec:
                                description: Exec specifies a command to execute in
                                  the container.
                                properties:
                                  command:
                                    description: |-
                                      Command is the command line to execute inside the container, the working directory for the
                                      command  is root ('/') in the container's filesystem. The command is simply exec'd, it is
                                      not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use
                                      a shell, you need to explicitly call out to that shell.
                                      Exit status of 0 is treated as live/healthy and non-zero is unhealthy.
                                    items:
                                      type: string
                                    type: array
                                    x-kubernetes-list-type: atomic
                                type: object
                              failureThreshold:
                                description: |-
                                  Minimum consecutive failures for the probe to be considered failed after having succeeded.
                                  Defaults to 3. Minimum value is 1.
                                format: int32
                                type: integer
                              grpc:
                                description: GRPC specifies a GRPC HealthCheckRequest.
                                properties:
                                  port:
                                    description: Port number of the gRPC service.
                                      Number must be in the range 1 to 65535.
                                    format: int32
                                    type: integer
                                  service:
                                    default: ""
                                    description: |-
                                      Service is the name of the service to place in the gRPC HealthCheckRequest
                                      (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).

                                      If this is not specified, the default behavior is defined by gRPC.
                                    type: string
                                required:
                                - port
                                type: object
                              httpGet:
                                description: HTTPGet specifies an HTTP GET request
                                  to perform.
                                properties:
                                  host:
                                    description: |-
                                      Host name to connect to, defaults to the pod IP. You probably want to set
                                      "Host" in httpHeaders instead.
                                    type: string
                                  httpHeaders:
                                    description: Custom headers to set in the request.
                                      HTTP allows repeated headers.
                                    items:
                                      description: HTTPHeader describes a custom header
                                        to be used in HTTP probes
                                      properties:
                                        name:
                                          description: |-
                                            The header field name.
                                            This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                          type: string
                                        value:
                                          description: The header field value
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  path:
                                    description: Path to access on the HTTP server.
                                    type: string
                                  port:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: |-
                                      Name or number of the port to access on the container.
                                      Number must be in the range 1 to 65535.
                                      Name must be an IANA_SVC_NAME.
                                    x-kubernetes-int-or-string: true
                                  scheme:
                                    description: |-
                                      Scheme to use for connecting to the host.
                                      Defaults to HTTP.
                                    type: string
                                required:
                                - port
                                type: object
                              initialDelaySeconds:
                                description: |-
                                  Number of seconds after the container has started before liveness probes are initiated.
                                  More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                                format: int32
                                type: integer
                              periodSeconds:
                                description: |-
                                  How often (in seconds) to perform the probe.
                                  Default to 10 seconds. Minimum value is 1.
                                format: int32
                                type: integer
                              successThreshold:
                                description: |-
                                  Minimum consecutive successes for the probe to be considered successful after having failed.
                                  Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                                format: int32
                                type: integer
                              tcpSocket:
                                description: TCPSocket specifies a connection to a
                                  TCP port.
                                properties:
                                  host:
                                    description: 'Optional: Host name to connect to,
                                      defaults to the pod IP.'
                                    type: string
                                  port:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: |-
                                      Number or name of the port to access on the container.
                                      Number must be in the range 1 to 65535.
                                      Name must be an IANA_SVC_NAME.
                                    x-kubernetes-int-or-string: true
                                required:
                                - port
                                type: object
                              terminationGracePeriodSeconds:
                                description: |-
                                  Optional duration in seconds the pod needs to terminate gracefully upon probe failure.
                                  The grace period is the duration in seconds after the processes running in the pod are sent
                                  a termination signal and the time when the processes are forcibly halted with a kill signal.
                                  Set this value longer than the expected cleanup time for your process.
                                  If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this
                                  value overrides the value provided by the pod spec.
                                  Value must be non-negative integer. The value zero indicates stop immediately via
                                  the kill signal (no opportunity to shut down).
                                  This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.
                                  Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.
                                format: int64
                                type: integer
                              timeoutSeconds:
                                description: |-
                                  Number of seconds after which the probe times out.
                                  Defaults to 1 second. Minimum value is 1.
                                  More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                                format: int32
                                type: integer
                            type: object
                        type: object
                      resourceLimits:
                        description: Resource limits for CPU/memory, etc.
                        properties:
                          cpu:
                            type: string
                          memory:
                            type: string
                        type: object
                      scaling:
                        description: Scaling configuration (only for non-task components).
                        properties:
                          hpa:
                            description: HPAConfig configures Horizontal Pod Autoscaling.
                            properties:
                              cpuThreshold:
                                format: int32
                                type: integer
                              maxReplicas:
                                format: int32
                                type: integer
                              memoryThreshold:
                                format: int32
                                type: integer
                              minReplicas:
                                format: int32
                                type: integer
                            type: object
                          s2z:
                            description: S2ZConfig configures scale-to-zero.
                            properties:
                              maxReplicas:
                                format: int32
                                type: integer
                              queueLength:
                                format: int32
                                type: integer
                            type: object
                        type: object
                      task:
                        description: Task configuration (mutually exclusive with scaling).
                        properties:
                          disabled:
                            type: boolean
                          schedule:
                            description: Only applicable for scheduled tasks.
                            properties:
                              cron:
                                type: string
                              timezone:
                                type: string
                            required:
                            - cron
                            type: object
                        type: object
                    type: object
                  dependencies:
                    description: Dependencies required by this component.
                    type: object
                  endpointTemplates:
                    description: A list of endpoints exposed by the component.
                    items:
                      description: EndpointTemplate represents an endpoint derived
                        from a component descriptor.
                      properties:
                        metadata:
                          description: Specification of the endpoint
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              type: object
                            finalizers:
                              items:
                                type: string
                              type: array
                            labels:
                              additionalProperties:
                                type: string
                              type: object
                            name:
                              type: string
                            namespace:
                              type: string
                          type: object
                        spec:
                          description: EndpointSpec defines the desired state of Endpoint
                          properties:
                            backendRef:
                              description: BackendRef is the reference to the backend
                                service
                              properties:
                                basePath:
                                  description: Base path of the upstream service
                                  type: string
                                componentRef:
                                  description: ComponentRef defines the component
                                    reference for the upstream service
                                  properties:
                                    port:
                                      format: int32
                                      type: integer
                                  required:
                                  - port
                                  type: object
                                target:
                                  description: Target defines the target service URL
                                    for the upstream service. This is used for proxies
                                  properties:
                                    url:
                                      description: URL of the upstream service
                                      type: string
                                  required:
                                  - url
                                  type: object
                                type:
                                  description: type of the upstream service
                                  enum:
                                  - componentRef
                                  - target
                                  type: string
                              required:
                              - type
                              type: object
                            networkVisibilities:
                              description: Network visibility levels that the endpoint
                                is exposed
                              properties:
                                organization:
                                  description: When enabled, the endpoint is accessible
                                    to other services within the same organization.
                                  properties:
                                    enable:
                                      type: boolean
                                    policies:
                                      items:
                                        description: Policy defines an API management
                                          policy for an endpoint
                                        properties:
                                          name:
                                            type: string
                                          policySpec:
                                            description: PolicySpec defines the configuration
                                              for different types of policies
                                            properties:
                                              apiKeyAuth:
                                                properties:
                                                  keySource:
                                                    properties:
                                                      header:
                                                        type: string
                                                      headerAuthScheme:
                                                        type: string
                                                    required:
                                                    - header
                                                    type: object
                                                  secretRefs:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - keySource
                                                - secretRefs
                                                type: object
                                              basicAuth:
                                                properties:
                                                  header:
                                                    type: string
                                                  headerAuthScheme:
                                                    type: string
                                                  users:
                                                    items:
                                                      properties:
                                                        passwordFromSecret:
                                                          type: string
                                                        username:
                                                          type: string
                                                      required:
                                                      - passwordFromSecret
                                                      - username
                                                      type: object
                                                    type: array
                                                required:
                                                - header
                                                - headerAuthScheme
                                                - users
                                                type: object
                                              cors:
                                                properties:
                                                  allowCredentials:
                                                    type: boolean
                                                  allowHeaders:
                                                    items:
                                                      type: string
                                                    type: array
                                                  allowMethods:
                                                    items:
                                                      type: string
                                                    type: array
                                                  allowOrigins:
                                                    items:
                                                      type: string
                                                    type: array
                                                  exposeHeaders:
                                                    items:
                                                      type: string
                                                    type: array
                                                  maxAge:
                                                    type: integer
                                                required:
                                                - allowCredentials
                                                - allowHeaders
                                                - allowMethods
                                                - allowOrigins
                                                - exposeHeaders
                                                - maxAge
                                                type: object
                                              mediationPolicies:
                                                items:
                                                  type: object
                                                type: array
                                              oauth2:
                                                description: OAuth2PolicySpec defines
                                                  the configuration for OAuth2 policies
                                                properties:
                                                  jwt:
                                                    properties:
                                                      authorization:
                                                        properties:
                                                          apiType:
                                                            enum:
                                                            - REST
                                                            - GRPC
                                                            - GraphQL
                                                            type: string
                                                          graphql:
                                                            properties:
                                                              claimsToHeaders:
                                                                items:
                                                                  properties:
                                                                    header:
                                                                      type: string
                                                                    name:
                                                                      type: string
                                                                  required:
                                                                  - header
                                                                  - name
                                                                  type: object
                                                                type: array
                                                              operations:
                                                                items:
                                                                  properties:
                                                                    name:
                                                                      type: string
                                                                    scopes:
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                    type:
                                                                      type: string
                                                                  required:
                                                                  - name
                                                                  - scopes
                                                                  - type
                                                                  type: object
                                                                type: array
                                                            required:
                                                            - claimsToHeaders
                                                            - operations
                                                            type: object
                                                          grpc:
                                                            properties:
                                                              claimsToHeaders:
                                                                items:
                                                                  properties:
                                                                    header:
                                                                      type: string
                                                                    name:
                                                                      type: string
                                                                  required:
                                                                  - header
                                                                  - name
                                                                  type: object
                                                                type: array
                                                              operations:
                                                                items:
                                                                  properties:
                                                                    methods:
                                                                      items:
                                                                        properties:
                                                                          name:
                                                                            type: string
                                                                          scopes:
                                                                            items:
                                                                              type: string
                                                                            type: array
                                                                        required:
                                                                        - name
                                                                        - scopes
                                                                        type: object
                                                                      type: array
                                                                    name:
                                                                      type: string
                                                                  required:
                                                                  - methods
                                                                  - name
                                                                  type: object
                                                                type: array
                                                            required:
                                                            - claimsToHeaders
                                                            - operations
                                                            type: object
                                                          rest:
                                                            properties:
                                                              claimsToHeaders:
                                                                items:
                                                                  properties:
                                                                    header:
                                                                      type: string
                                                                    name:
                                                                      type: string
                                                                  required:
                                                                  - header
                                                                  - name
                                                                  type: object
                                                                type: array
                                                              operations:
                                                                items:
                                                                  properties:
                                                                    method:
                                                                      description: |-
                                                                        HTTPMethod describes how to select a HTTP route by matching the HTTP
                                                                        method as defined by
                                                                        [RFC 7231](https://datatracker.ietf.org/doc/html/rfc7231#section-4) and
                                                                        [RFC 5789](https://datatracker.ietf.org/doc/html/rfc5789#section-2).
                                                                        The value is expected in upper case.
                                                                      enum:
                                                                      - GET
                                                                      - HEAD
                                                                      - POST
                                                                      - PUT
                                                                      - DELETE
                                                                      - CONNECT
                                                                      - OPTIONS
                                                                      - TRACE
                                                                      - PATCH
                                                                      type: string
                                                                    scopes:
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                    target:
                                                                      type: string
                                                                  required:
                                                                  - method
                                                                  - scopes
                                                                  - target
                                                                  type: object
                                                                type: array
                                                            required:
                                                            - claimsToHeaders
                                                            - operations
                                                            type: object
                                                        required:
                                                        - apiType
                                                        type: object
                                                      claims:
                                                        items:
                                                          properties:
                                                            key:
                                                              type: string
                                                            values:
                                                              items:
                                                                type: string
                                                              type: array
                                                          required:
                                                          - key
                                                          - values
                                                          type: object
                                                        type: array
                                                    required:
                                                    - authorization
                                                    type: object
                                                required:
                                                - jwt
                                                type: object
                                              rateLimit:
                                                properties:
                                                  apiLevel:
                                                    properties:
                                                      requestLimit:
                                                        type: integer
                                                      timeUnit:
                                                        type: string
                                                    required:
                                                    - requestLimit
                                                    - timeUnit
                                                    type: object
                                                  operationLevel:
                                                    properties:
                                                      rest:
                                                        items:
                                                          properties:
                                                            method:
                                                              type: string
                                                            requestLimit:
                                                              type: integer
                                                            target:
                                                              type: string
                                                            timeUnit:
                                                              type: string
                                                          required:
                                                          - method
                                                          - requestLimit
                                                          - target
                                                          - timeUnit
                                                          type: object
                                                        type: array
                                                    required:
                                                    - rest
                                                    type: object
                                                required:
                                                - apiLevel
                                                - operationLevel
                                                type: object
                                            type: object
                                          type:
                                            description: PolicyType defines the type
                                              of API management policy
                                            type: string
                                        required:
                                        - name
                                        - policySpec
                                        - type
                                        type: object
                                      type: array
                                  required:
                                  - enable
                                  type: object
                                public:
                                  description: When enabled, the endpoint becomes
                                    accessible externally
                                  properties:
                                    enable:
                                      type: boolean
                                    policies:
                                      items:
                                        description: Policy defines an API management
                                          policy for an endpoint
                                        properties:
                                          name:
                                            type: string
                                          policySpec:
                                            description: PolicySpec defines the configuration
                                              for different types of policies
                                            properties:
                                              apiKeyAuth:
                                                properties:
                                                  keySource:
                                                    properties:
                                                      header:
                                                        type: string
                                                      headerAuthScheme:
                                                        type: string
                                                    required:
                                                    - header
                                                    type: object
                                                  secretRefs:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - keySource
                                                - secretRefs
                                                type: object
                                              basicAuth:
                                                properties:
                                                  header:
                                                    type: string
                                                  headerAuthScheme:
                                                    type: string
                                                  users:
                                                    items:
                                                      properties:
                                                        passwordFromSecret:
                                                          type: string
                                                        username:
                                                          type: string
                                                      required:
                                                      - passwordFromSecret
                                                      - username
                                                      type: object
                                                    type: array
                                                required:
                                                - header
                                                - headerAuthScheme
                                                - users
                                                type: object
                                              cors:
                                                properties:
                                                  allowCredentials:
                                                    type: boolean
                                                  allowHeaders:
                                                    items:
                                                      type: string
                                                    type: array
                                                  allowMethods:
                                                    items:
                                                      type: string
                                                    type: array
                                                  allowOrigins:
                                                    items:
                                                      type: string
                                                    type: array
                                                  exposeHeaders:
                                                    items:
                                                      type: string
                                                    type: array
                                                  maxAge:
                                                    type: integer
                                                required:
                                                - allowCredentials
                                                - allowHeaders
                                                - allowMethods
                                                - allowOrigins
                                                - exposeHeaders
                                                - maxAge
                                                type: object
                                              mediationPolicies:
                                                items:
                                                  type: object
                                                type: array
                                              oauth2:
                                                description: OAuth2PolicySpec defines
                                                  the configuration for OAuth2 policies
                                                properties:
                                                  jwt:
                                                    properties:
                                                      authorization:
                                                        properties:
                                                          apiType:
                                                            enum:
                                                            - REST
                                                            - GRPC
                                                            - GraphQL
                                                            type: string
                                                          graphql:
                                                            properties:
                                                              claimsToHeaders:
                                                                items:
                                                                  properties:
                                                                    header:
                                                                      type: string
                                                                    name:
                                                                      type: string
                                                                  required:
                                                                  - header
                                                                  - name
                                                                  type: object
                                                                type: array
                                                              operations:
                                                                items:
                                                                  properties:
                                                                    name:
                                                                      type: string
                                                                    scopes:
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                    type:
                                                                      type: string
                                                                  required:
                                                                  - name
                                                                  - scopes
                                                                  - type
                                                                  type: object
                                                                type: array
                                                            required:
                                                            - claimsToHeaders
                                                            - operations
                                                            type: object
                                                          grpc:
                                                            properties:
                                                              claimsToHeaders:
                                                                items:
                                                                  properties:
                                                                    header:
                                                                      type: string
                                                                    name:
                                                                      type: string
                                                                  required:
                                                                  - header
                                                                  - name
                                                                  type: object
                                                                type: array
                                                              operations:
                                                                items:
                                                                  properties:
                                                                    methods:
                                                                      items:
                                                                        properties:
                                                                          name:
                                                                            type: string
                                                                          scopes:
                                                                            items:
                                                                              type: string
                                                                            type: array
                                                                        required:
                                                                        - name
                                                                        - scopes
                                                                        type: object
                                                                      type: array
                                                                    name:
                                                                      type: string
                                                                  required:
                                                                  - methods
                                                                  - name
                                                                  type: object
                                                                type: array
                                                            required:
                                                            - claimsToHeaders
                                                            - operations
                                                            type: object
                                                          rest:
                                                            properties:
                                                              claimsToHeaders:
                                                                items:
                                                                  properties:
                                                                    header:
                                                                      type: string
                                                                    name:
                                                                      type: string
                                                                  required:
                                                                  - header
                                                                  - name
                                                                  type: object
                                                                type: array
                                                              operations:
                                                                items:
                                                                  properties:
                                                                    method:
                                                                      description: |-
                                                                        HTTPMethod describes how to select a HTTP route by matching the HTTP
                                                                        method as defined by
                                                                        [RFC 7231](https://datatracker.ietf.org/doc/html/rfc7231#section-4) and
                                                                        [RFC 5789](https://datatracker.ietf.org/doc/html/rfc5789#section-2).
                                                                        The value is expected in upper case.
                                                                      enum:
                                                                      - GET
                                                                      - HEAD
                                                                      - POST
                                                                      - PUT
                                                                      - DELETE
                                                                      - CONNECT
                                                                      - OPTIONS
                                                                      - TRACE
                                                                      - PATCH
                                                                      type: string
                                                                    scopes:
                                                                      items:
                                                                        type: string
                                                                      type: array
                                                                    target:
                                                                      type: string
                                                                  required:
                                                                  - method
                                                                  - scopes
                                                                  - target
                                                                  type: object
                                                                type: array
                                                            required:
                                                            - claimsToHeaders
                                                            - operations
                                                            type: object
                                                        required:
                                                        - apiType
                                                        type: object
                                                      claims:
                                                        items:
                                                          properties:
                                                            key:
                                                              type: string
                                                            values:
                                                              items:
                                                                type: string
                                                              type: array
                                                          required:
                                                          - key
                                                          - values
                                                          type: object
                                                        type: array
                                                    required:
                                                    - authorization
                                                    type: object
                                                required:
                                                - jwt
                                                type: object
                                              rateLimit:
                                                properties:
                                                  apiLevel:
                                                    properties:
                                                      requestLimit:
                                                        type: integer
                                                      timeUnit:
                                                        type: string
                                                    required:
                                                    - requestLimit
                                                    - timeUnit
                                                    type: object
                                                  operationLevel:
                                                    properties:
                                                      rest:
                                                        items:
                                                          properties:
                                                            method:
                                                              type: string
                                                            requestLimit:
                                                              type: integer
                                                            target:
                                                              type: string
                                                            timeUnit:
                                                              type: string
                                                          required:
                                                          - method
                                                          - requestLimit
                                                          - target
                                                          - timeUnit
                                                          type: object
                                                        type: array
                                                    required:
                                                    - rest
                                                    type: object
                                                required:
                                                - apiLevel
                                                - operationLevel
                                                type: object
                                            type: object
                                          type:
                                            description: PolicyType defines the type
                                              of API management policy
                                            type: string
                                        required:
                                        - name
                                        - policySpec
                                        - type
                                        type: object
                                      type: array
                                  required:
                                  - enable
                                  type: object
                              type: object
                            type:
                              description: Type indicates the protocol of the endpoint
                              enum:
                              - HTTP
                              - REST
                              - gRPC
                              - GraphQL
                              - Websocket
                              - TCP
                              - UDP
                              type: string
                          required:
                          - backendRef
                          - type
                          type: object
                      required:
                      - metadata
                      - spec
                      type: object
                    type: array
                type: object
              targetArtifact:
                description: DeployableArtifactSpec defines the spec section of DeployableArtifact.
                properties:
                  fromBuildRef:
                    description: Mutually exclusive references to a build or an image.
                    properties:
                      gitRevision:
                        description: GitRevision to select the latest Build that matches
                          it.
                        type: string
                      name:
                        description: Name of the referenced Build resource.
                        type: string
                    type: object
                  fromImageRef:
                    description: Mutually exclusive references to a specific image
                      tag.
                    properties:
                      skipVersionValidation:
                        description: Whether to skip version validation (for semantic
                          version compliance).
                        type: boolean
                      tag:
                        description: Name of the image tag (e.g., “1.2.0”, “latest”,
                          etc.).
                        type: string
                    type: object
                type: object
            required:
            - targetArtifact
            type: object
          status:
            description: DeployableArtifactStatus defines the observed state of DeployableArtifact.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              observedGeneration:
                description: |-
                  INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                format: int64
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
