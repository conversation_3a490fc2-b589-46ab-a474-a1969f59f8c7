apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    cert-manager.io/inject-ca-from: choreo-system/choreo-serving-cert
    controller-gen.kubebuilder.io/version: v0.16.4
  name: components.core.choreo.dev
spec:
  group: core.choreo.dev
  names:
    kind: Component
    listKind: ComponentList
    plural: components
    shortNames:
    - comp
    - comps
    singular: component
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: Component is the Schema for the components API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ComponentSpec defines the desired state of Component.
            properties:
              source:
                description: Source the source information of the component where
                  the code or image is retrieved.
                properties:
                  containerRegistry:
                    description: |-
                      ContainerRegistry specifies the configuration for the component source to be a container image indicating
                      that the component should be deployed using the provided image.
                      This field is mutually exclusive with the other source types.
                    properties:
                      authentication:
                        description: Authentication information to access the container
                          registry.
                        properties:
                          secretRef:
                            description: Reference to the secret that contains the
                              container registry authentication info.
                            type: string
                        type: object
                      imageName:
                        description: |-
                          Image name of the container image. Format: <registry>/<image> without the tag.
                          Example: docker.io/library/nginx
                        type: string
                    type: object
                  gitRepository:
                    description: |-
                      GitRepository specifies the configuration for the component source to be a Git repository indicating
                      that the component should be built from the source code.
                      This field is mutually exclusive with the other source types.
                    properties:
                      authentication:
                        description: |-
                          Authentication the authentication information to access the Git repository
                          If not provided, the Git repository should be public
                        properties:
                          secretRef:
                            description: SecretRef is a reference to the secret containing
                              Git credentials
                            type: string
                        required:
                        - secretRef
                        type: object
                      url:
                        description: |-
                          URL the Git repository URL
                          Examples:
                          - https://github.com/jhonb2077/customer-service
                          - https://gitlab.com/jhonb2077/customer-service
                        type: string
                    required:
                    - url
                    type: object
                type: object
              type:
                description: Type of the component that indicates how the component
                  deployed.
                type: string
            type: object
          status:
            description: ComponentStatus defines the observed state of Component.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              observedGeneration:
                format: int64
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
