# This is the default values.yaml file for the Choreo platform Helm chart.
# The values.yaml is generated by the helmify and append content of this file to the generated values.yaml
# Therefore, all the manual changes to the values.yaml should do here.
choreoDefaultValues:
  enableDefaultOrgAndProject: true
  organization:
    enabled: true
    name: default-org
    displayName: Default Organization
    description: Getting started with your first organization
  dataPlane:
    name: default-dataplane
    namespace: default-org
    organization: default-org
  Environments:
    - name: development
      displayName: Development
      description: Development environment for testing
      namespace: default-org
      organization: default-org
      dnsPrefix: dev
      isCritical: false
    - name: staging
      displayName: Staging
      description: Staging environment for pre-production testing
      namespace: default-org
      organization: default-org
      dnsPrefix: staging
      isCritical: false
    - name: production
      displayName: Production
      description: Production environment
      namespace: default-org
      organization: default-org
      dnsPrefix: prod
      isCritical: true
  deploymentPipeline:
    name: default-pipeline
    namespace: default-org
    organization: default-org
    displayName: Default Pipeline
    description: Standard deployment pipeline with dev, staging, and prod environments
    promotionOrder:
      - sourceEnvironmentRef: development
        targetEnvironmentRefs:
          - name: staging
            requiresApproval: false
          - name: production
            isManualApprovalRequired: true
      - sourceEnvironmentRef: staging
        targetEnvironmentRefs:
            - name: production
              requiresApproval: true
  DefaultProject:
    name: default-project
    namespace: default-org
    organization: default-org
    displayName: Default Project
    description: Your first project to get started
waitJob:
  image: bitnami/kubectl:latest
metricsServer:
  enabled: false
  kubeletInsecureTlsEnabled: true
# customizing the cert-manager configurations
certmanager:
  enabled: true
  crds:
    enabled: true
  # -- Resource limits and requests for the cert-manager controller
  resources:
    requests:
      cpu: 10m
      memory: 32Mi
    limits:
      cpu: 50m
      memory: 64Mi
  cainjector:
    # -- Resource limits and requests for the cert-manager cainjector
    resources:
      requests:
        cpu: 10m
        memory: 32Mi
      limits:
        cpu: 50m
        memory: 64Mi
  webhook:
    # -- Resource limits and requests for the cert-manager webhook
    resources:
      requests:
        cpu: 10m
        memory: 32Mi
      limits:
        cpu: 50m
        memory: 64Mi
