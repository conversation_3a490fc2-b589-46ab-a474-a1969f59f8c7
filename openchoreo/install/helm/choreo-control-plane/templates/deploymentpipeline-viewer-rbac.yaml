apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-deploymentpipeline-viewer-role
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - deploymentpipelines
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - deploymentpipelines/status
  verbs:
  - get
