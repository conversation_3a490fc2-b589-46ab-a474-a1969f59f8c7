apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-leader-election-role
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-leader-election-rolebinding
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: '{{ include "choreo-control-plane.fullname" . }}-leader-election-role'
subjects:
- kind: ServiceAccount
  name: '{{ include "choreo-control-plane.fullname" . }}-controller-manager'
  namespace: '{{ .Release.Namespace }}'
