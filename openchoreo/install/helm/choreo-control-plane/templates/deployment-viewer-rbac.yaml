apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-deployment-viewer-role
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - deployments
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - deployments/status
  verbs:
  - get
