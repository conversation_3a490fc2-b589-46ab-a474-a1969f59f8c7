apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-component-editor-role
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - components
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - components/status
  verbs:
  - get
