apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-project-editor-role
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
rules:
- apiGroups:
  - core.choreo.dev
  resources:
  - projects
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - core.choreo.dev
  resources:
  - projects/status
  verbs:
  - get
