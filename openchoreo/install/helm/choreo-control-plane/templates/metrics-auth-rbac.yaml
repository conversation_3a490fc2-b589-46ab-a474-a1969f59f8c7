apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-metrics-auth-role
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
rules:
- apiGroups:
  - authentication.k8s.io
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups:
  - authorization.k8s.io
  resources:
  - subjectaccessreviews
  verbs:
  - create
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-metrics-auth-rolebinding
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: '{{ include "choreo-control-plane.fullname" . }}-metrics-auth-role'
subjects:
- kind: ServiceAccount
  name: '{{ include "choreo-control-plane.fullname" . }}-controller-manager'
  namespace: '{{ .Release.Namespace }}'
