apiVersion: v1
kind: Service
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-webhook-service
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
spec:
  type: {{ .Values.webhookService.type }}
  selector:
    control-plane: controller-manager
    {{- include "choreo-control-plane.selectorLabels" . | nindent 4 }}
  ports:
  {{- .Values.webhookService.ports | toYaml | nindent 2 }}
