apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-controller-manager
  labels:
    control-plane: controller-manager
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.controllerManager.replicas }}
  selector:
    matchLabels:
      control-plane: controller-manager
    {{- include "choreo-control-plane.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        control-plane: controller-manager
      {{- include "choreo-control-plane.selectorLabels" . | nindent 8 }}
      annotations:
        kubectl.kubernetes.io/default-container: manager
    spec:
      containers:
      - args: {{- toYaml .Values.controllerManager.manager.args | nindent 8 }}
        command:
        - /manager
        env:
        - name: KUBERNETES_CLUSTER_DOMAIN
          value: {{ quote .Values.kubernetesClusterDomain }}
        image: {{ .Values.controllerManager.manager.image.repository }}:{{ .Values.controllerManager.manager.image.tag
          | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.controllerManager.manager.imagePullPolicy }}
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        ports:
        - containerPort: 9443
          name: webhook-server
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources: {{- toYaml .Values.controllerManager.manager.resources | nindent 10
          }}
        securityContext: {{- toYaml .Values.controllerManager.manager.containerSecurityContext
          | nindent 10 }}
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
      securityContext: {{- toYaml .Values.controllerManager.podSecurityContext | nindent
        8 }}
      serviceAccountName: {{ include "choreo-control-plane.fullname" . }}-controller-manager
      terminationGracePeriodSeconds: 10
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName: webhook-server-cert
