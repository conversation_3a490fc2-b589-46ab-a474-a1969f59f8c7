{{ if .Values.choreoDefaultValues.enableDefaultOrgAndProject }}
apiVersion: core.choreo.dev/v1
kind: DeploymentPipeline
metadata:
  name: {{ .Values.choreoDefaultValues.deploymentPipeline.name }}
  namespace: {{ .Values.choreoDefaultValues.deploymentPipeline.namespace }}
  annotations:
    core.choreo.dev/display-name: {{ .Values.choreoDefaultValues.deploymentPipeline.displayName }}
    core.choreo.dev/description: {{ .Values.choreoDefaultValues.deploymentPipeline.description }}
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "10"
  labels:
    core.choreo.dev/organization: {{ .Values.choreoDefaultValues.deploymentPipeline.organization }}
    core.choreo.dev/name: {{ .Values.choreoDefaultValues.deploymentPipeline.name }}
spec:
  promotionPaths:
  {{- toYaml .Values.choreoDefaultValues.deploymentPipeline.promotionOrder | nindent 4 }}
{{ end }}
