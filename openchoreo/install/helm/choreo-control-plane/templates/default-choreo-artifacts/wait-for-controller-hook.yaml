{{ if .Values.choreoDefaultValues.enableDefaultOrgAndProject }}
apiVersion: batch/v1
kind: Job
metadata:
  name: wait-for-controller-ready
  annotations:
    "helm.sh/hook": post-install, post-upgrade
    "helm.sh/hook-weight": "6"
    "helm.sh/hook-delete-policy": hook-succeeded,hook-failed
spec:
  template:
    spec:
      serviceAccountName: controller-wait-sa
      containers:
        - name: wait-for-ready
          image: {{ .Values.waitJob.image }}
          imagePullPolicy: IfNotPresent
          command:
            - /bin/sh
            - -c
            - |
              echo "Waiting for choreo controller to be ready..."
              kubectl wait --namespace {{ .Release.Namespace }} \
              --for=condition=Ready pod \
              -l app.kubernetes.io/name=choreo-control-plane,control-plane=controller-manager \
              --timeout=900s
      restartPolicy: Never
  backoffLimit: 5
{{ end }}
