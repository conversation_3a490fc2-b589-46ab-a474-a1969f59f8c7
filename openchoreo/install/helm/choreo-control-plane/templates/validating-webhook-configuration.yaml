apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-validating-webhook-configuration
  annotations:
    cert-manager.io/inject-ca-from: {{ .Release.Namespace }}/{{ include "choreo-control-plane.fullname" . }}-serving-cert
  labels:
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: '{{ include "choreo-control-plane.fullname" . }}-webhook-service'
      namespace: '{{ .Release.Namespace }}'
      path: /validate-core-choreo-dev-v1-project
  failurePolicy: Fail
  name: vproject-v1.kb.io
  rules:
  - apiGroups:
    - core.choreo.dev
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - projects
  sideEffects: None
