apiVersion: v1
kind: Service
metadata:
  name: {{ include "choreo-control-plane.fullname" . }}-controller-manager-metrics-service
  labels:
    control-plane: controller-manager
  {{- include "choreo-control-plane.labels" . | nindent 4 }}
spec:
  type: {{ .Values.metricsService.type }}
  selector:
    control-plane: controller-manager
    {{- include "choreo-control-plane.selectorLabels" . | nindent 4 }}
  ports:
  {{- .Values.metricsService.ports | toYaml | nindent 2 }}
