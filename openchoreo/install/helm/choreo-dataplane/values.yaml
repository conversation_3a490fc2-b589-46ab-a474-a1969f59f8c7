controllerManager:
  manager:
    args:
    - --metrics-bind-address=:8443
    - --leader-elect
    - --health-probe-bind-address=:8081
    containerSecurityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
    image:
      repository: ghcr.io/openchoreo/controller
      tag: latest
    imagePullPolicy: IfNotPresent
    resources:
      limits:
        cpu: 500m
        memory: 128Mi
      requests:
        cpu: 10m
        memory: 64Mi
  podSecurityContext:
    runAsNonRoot: true
  replicas: 1
  serviceAccount:
    annotations: {}
kubernetesClusterDomain: cluster.local
metricsService:
  ports:
  - name: https
    port: 8443
    protocol: TCP
    targetPort: 8443
  type: ClusterIP
webhookService:
  ports:
  - port: 443
    protocol: TCP
    targetPort: 9443
  type: ClusterIP
waitJob:
  image: bitnami/kubectl:latest
metricsServer:
  enabled: false
  kubeletInsecureTlsEnabled: true

# customizing the vault configurations
vault:
  server:
    # -- Resource limits and requests for the vault server
    resources:
      requests:
        memory: 64Mi
        cpu: 50m
      limits:
        memory: 128Mi
        cpu: 100m
    dev:
      enabled: true
      devRootToken: "root"
      logLevel: "info"
    # Add readiness probe configuration
    readinessProbe:
      exec:
        command: ["/bin/sh", "-ec", "vault status -tls-skip-verify"]
      initialDelaySeconds: 5
      timeoutSeconds: 10  # Increase from default
      periodSeconds: 10
      failureThreshold: 3
  injector:
    enabled: false
    # -- Resource limits and requests for the vault injector
    resources:
      requests:
        memory: 64Mi
        cpu: 50m
      limits:
        memory: 128Mi
        cpu: 100m

# customizing the secrets-store-csi-driver configurations
secrets-store-csi-driver:
  syncSecret:
    enabled: true
  enableSecretRotation: true
  vaultCsiProvider:
    enabled: true

# customizing the registry configurations
registry:
  enabled: true
  # -- Resource limits and requests for the registry
  resources:
    limits:
      memory: 256Mi
      cpu: 100m
    requests:
      memory: 128Mi
      cpu: 50m
  service:
    # -- NodePort for the registry service
    nodePort: 30003
  # -- Persistent volume storage for the registry
  storage:
    size: 2Gi

# customizing the envoy gateway configurations
gateway-helm:
  config:
    envoyGateway:
      rateLimit:
        backend:
          type: Redis
          redis:
            url: redis.choreo-system.svc.cluster.local:6379
  # -- Resource limits and requests for the gateway
  deployment:
    envoyGateway:
      resources:
        limits:
          cpu: 200m
          memory: 256Mi
        requests:
          cpu: 100m
          memory: 128Mi

# customizing the cert-manager configurations
certmanager:
  enabled: true
  crds:
    enabled: true
  # -- Resource limits and requests for the cert-manager controller
  resources:
    requests:
      cpu: 10m
      memory: 32Mi
    limits:
      cpu: 50m
      memory: 64Mi
  cainjector:
    # -- Resource limits and requests for the cert-manager cainjector
    resources:
      requests:
        cpu: 10m
        memory: 32Mi
      limits:
        cpu: 50m
        memory: 64Mi
  webhook:
    # -- Resource limits and requests for the cert-manager webhook
    resources:
      requests:
        cpu: 10m
        memory: 32Mi
      limits:
        cpu: 50m
        memory: 64Mi

# customizing the argo workflows configurations
argo-workflows:
  controller:
    # -- Resource limits and requests for the argo workflows controller
    resources:
      limits:
        memory: 64Mi
        cpu: 50m
      requests:
        memory: 32Mi
        cpu: 25m
  server:
    enabled: false
  crds:
    keep: false
  workflow:
    serviceAccount:
      create: true
  workflowNamespaces:
    - argo-build

# Customizing overall observability configurations
observability:
  logging:
    enabled: false

# Customizing OpenSearch configurations
opensearch:
  image:
    repository: opensearchproject/opensearch
    tag: "2.11.0"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    httpPort: 9200
    transportPort: 9300
  
  config:
    clusterName: opensearch-cluster
    discoveryType: single-node
    javaOpts: "-Xms512m -Xmx512m"
    memoryLock: false
    disableSecurity: true
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 512Mi
  
  persistence:
    enabled: true
    storageClass: ""
    accessMode: ReadWriteOnce
    size: 5Gi
  
  replicas: 1

# Customizing OpenSearch Dashboards configurations
opensearchDashboard:
  image:
    repository: opensearchproject/opensearch-dashboards
    tag: "2.11.0"
    pullPolicy: IfNotPresent
  
  service:
    type: NodePort
    port: 5601
  
  config:
    # This will be templated to use the correct service name
    opensearchHosts: '["http://{{ include "opensearch-logging.fullname" . }}-opensearch:9200"]'
    disableSecurity: true
  
  replicas: 1

# Customizing Fluent Bit configurations
fluentBit:
  image:
    repository: fluent/fluent-bit
    tag: "2.1.10"
    pullPolicy: IfNotPresent
  
  config:
    service:
      flush: 1
      logLevel: info
      daemon: off
    
    input:
      name: tail
      tag: "kube.*"
      path: "/var/log/containers/*_choreo-system_*.log,/var/log/containers/*_dp-*_*.log"
      excludePath: "/var/log/containers/*opensearch-0*_choreo-system_*.log,/var/log/containers/*opensearch-dashboard*_choreo-system_*.log,/var/log/containers/*fluent-bit*_choreo-system_*.log"
      parser: docker
      inotifyWatcher: false
      db: "/var/log/flb_kube.db"
      memBufLimit: "256MB"
      skipLongLines: true
      refreshInterval: 10
    
    filter:
      name: kubernetes
      match: "kube.*"
      kubeURL: "https://kubernetes.default.svc:443"
      kubeCAFile: "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
      kubeTokenFile: "/var/run/secrets/kubernetes.io/serviceaccount/token"
      kubeTagPrefix: "kube.var.log.containers."
      mergeLog: true
      mergeLogKey: "log_processed"
      k8sLoggingParser: true
      k8sLoggingExclude: false
    
    output:
      name: opensearch
      match: "kube.*"
      host: opensearch
      port: 9200
      index: kubernetes_cluster
      type: flb_type
      logstashFormat: true
      logstashPrefix: kubernetes
      timeKey: "@timestamp"
      traceError: true
      httpUser: admin
      httpPasswd: admin
      tls: false
      tlsVerify: false
      suppressTypeName: true
    
    parser:
      name: docker
      format: json
      timeKey: time
      timeFormat: "%Y-%m-%dT%H:%M:%S.%L"
      timeKeep: true
  
  rbac:
    create: true
    serviceAccountName: fluent-bit
  
  hostPaths:
    varLog: /var/log
    dockerContainers: /var/lib/docker/containers

