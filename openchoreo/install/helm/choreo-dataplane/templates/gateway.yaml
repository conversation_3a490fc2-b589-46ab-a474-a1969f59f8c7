apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: gateway
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: gateway-external
spec:
  gatewayClassName: gateway
  infrastructure:
    parametersRef:
      group: gateway.envoyproxy.io
      kind: EnvoyProxy
      name: choreo-external-gateway
  listeners:
    - name: https
      protocol: HTTPS
      port: 443
      allowedRoutes:
        namespaces:
          from: All
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: envoy-gateway-tls-secret
            namespace: {{ $.Values.namespace | default $.Release.Namespace }}
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: gateway-internal
spec:
  gatewayClassName: gateway
  infrastructure:
    parametersRef:
      group: gateway.envoyproxy.io
      kind: EnvoyProxy
      name: choreo-internal-gateway
  listeners:
    - name: https
      protocol: HTTPS
      port: 443
      allowedRoutes:
        namespaces:
          from: All
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: envoy-gateway-tls-secret
            namespace: {{ $.Values.namespace | default $.Release.Namespace }}
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyProxy
metadata:
  name: choreo-internal-gateway
  namespace: {{ $.Values.namespace | default $.Release.Namespace }}
spec:
  provider:
    type: Kubernetes
    kubernetes:
      envoyService:
        name: choreo-internal-gateway
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyProxy
metadata:
  name: choreo-external-gateway
  namespace: {{ $.Values.namespace | default $.Release.Namespace }}
spec:
  provider:
    type: Kubernetes
    kubernetes:
      envoyService:
        name: choreo-external-gateway
