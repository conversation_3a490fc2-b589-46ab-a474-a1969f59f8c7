apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ include "choreo-dataplane.fullname" . }}-serving-cert
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "2"
  labels:
  {{- include "choreo-dataplane.labels" . | nindent 4 }}
spec:
  dnsNames:
  - '{{ include "choreo-dataplane.fullname" . }}-webhook-service.{{ .Release.Namespace }}.svc'
  - '{{ include "choreo-dataplane.fullname" . }}-webhook-service.{{ .Release.Namespace }}.svc.{{
    .Values.kubernetesClusterDomain }}'
  issuerRef:
    kind: Issuer
    name: '{{ include "choreo-dataplane.fullname" . }}-selfsigned-issuer'
  secretName: webhook-server-cert
