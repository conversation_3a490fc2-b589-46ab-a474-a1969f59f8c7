{{ if .Values.registry.enabled }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: container-registry-shared-pv
spec:
  capacity:
    storage: {{ .Values.registry.storage.size }}
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: manual
  hostPath:
    path: /mnt/shared
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: container-registry-shared-pvc
  namespace: {{ .Release.Namespace }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: {{ .Values.registry.storage.size }}
  storageClassName: manual
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: registry
  namespace: {{ .Release.Namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: registry
  template:
    metadata:
      labels:
        app: registry
    spec:
      containers:
      - name: registry
        image: registry:2
        resources:
          {{- toYaml .Values.registry.resources | nindent 12 }}
        ports:
        - containerPort: 5000
        env:
        - name: REGISTRY_HTTP_ADDR
          value: "0.0.0.0:5000"
        - name: REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY
          value: /var/lib/registry
        volumeMounts:
        - name: registry-storage
          mountPath: /var/lib/registry
      volumes:
      - name: registry-storage
        persistentVolumeClaim:
          claimName: container-registry-shared-pvc
{{ end }}
