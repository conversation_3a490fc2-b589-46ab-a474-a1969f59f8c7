apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name:  {{ include "choreo-dataplane.fullname" . }}-csi-secrets-store-token-creator
rules:
- apiGroups: [""]
  resources: ["serviceaccounts/token"]
  verbs: ["create"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name:  {{ include "choreo-dataplane.fullname" . }}-csi-secrets-store-token-creator
subjects:
- kind: ServiceAccount
  name: default
  namespace:  {{ .Release.Namespace }}
roleRef:
  kind: ClusterRole
  name:  {{ include "choreo-dataplane.fullname" . }}-csi-secrets-store-token-creator
  apiGroup: rbac.authorization.k8s.io
