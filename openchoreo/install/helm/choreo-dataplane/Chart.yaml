apiVersion: v2
name: choreo-dataplane
description: A Helm chart for openChoreo dataplane
# A chart can be either an 'application' or a 'library' chart.
#
# Application charts are a collection of templates that can be packaged into versioned archives
# to be deployed.
#
# Library charts provide useful utilities or functions for the chart developer. They're included as
# a dependency of application charts to inject those utilities and functions into the rendering
# pipeline. Library charts do not define any templates and therefore cannot be deployed.
type: application
# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 0.0.0-latest-dev
# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: "latest-dev"
dependencies:
  - name: vault
    version: 0.29.1
    repository: "https://helm.releases.hashicorp.com"
    condition: vault.enabled
  - name: secrets-store-csi-driver
    repository: "https://kubernetes-sigs.github.io/secrets-store-csi-driver/charts"
    version: "1.4.0"
    condition: secrets-store-csi-driver.enabled
  - name: gateway-helm
    repository: "oci://registry-1.docker.io/envoyproxy"
    version: 1.2.3
  - name: cert-manager
    repository: https://charts.jetstack.io
    condition: certmanager.enabled
    alias: certmanager
    version: "v1.16.2"
  - name: argo-workflows
    repository: "https://argoproj.github.io/argo-helm"
    version: 0.45.2
