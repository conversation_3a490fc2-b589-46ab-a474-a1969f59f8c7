# Default values for cilium.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

cilium:
  encryption:
    enabled: true
    type: wireguard
  envoyConfig:
    enabled: false
  envoy:
    enabled: false
  nodePort:
    enabled: true
  priorityClassName: system-node-critical
  hubble:
    relay:
      enabled: false
    ui:
      enabled: false
  resources:
    limits:
      memory: 256Mi
      cpu: 250m
    requests:
      memory: 124Mi
      cpu: 100m
  operator:
    replicas: 1
    rollOutPods: true
    resources:
      limits:
        memory: 128Mi
        cpu: 100m
      requests:
        memory: 64Mi
        cpu: 50m
    unmanagedPodWatcher:
      restart: true
    priorityClassName: system-cluster-critical
  startupProbe:
    failureThreshold: 600
  policyEnforcementMode: default
  routingMode: tunnel
  kubeProxyReplacement: false
  rollOutCiliumPods: true
waitJob:
  enabled: true
  image: bitnami/kubectl:latest
