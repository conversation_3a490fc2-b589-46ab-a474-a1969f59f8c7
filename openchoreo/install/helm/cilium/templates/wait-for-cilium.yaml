{{ if .Values.waitJob.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: wait-for-cilium-ready
  annotations:
    "helm.sh/hook": post-install
    "helm.sh/hook-delete-policy": hook-succeeded,hook-failed
spec:
  template:
    spec:
      serviceAccountName: cilium-wait-sa
      containers:
      - name: wait-for-ready
        image: {{ .Values.waitJob.image }}
        imagePullPolicy: IfNotPresent
        command:
        - /bin/sh
        - -c
        - |
          echo "Waiting for Cilium pods to be ready..."
          kubectl wait --namespace {{ .Release.Namespace }} \
            --for=condition=Ready pods --all --timeout=900s
      restartPolicy: Never
  backoffLimit: 5
{{ end }}
