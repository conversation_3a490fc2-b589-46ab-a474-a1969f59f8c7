---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cilium-wait-sa
  namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: cilium-wait-role
  namespace: {{ .Release.Namespace }}
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cilium-wait-rolebinding
  namespace: {{ .Release.Namespace }}
subjects:
- kind: ServiceAccount
  name: cilium-wait-sa
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: Role
  name: cilium-wait-role
  apiGroup: rbac.authorization.k8s.io
