kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
name: choreo-dp
nodes:
- role: control-plane
  image: kindest/node:v1.32.0@sha256:c48c62eac5da28cdadcf560d1d8616cfa6783b58f0d94cf63ad1bf49600cb027
- role: worker
  labels:
    core.choreo.dev/noderole: workflow-runner
  image: kindest/node:v1.32.0@sha256:c48c62eac5da28cdadcf560d1d8616cfa6783b58f0d94cf63ad1bf49600cb027
  extraMounts:
    - hostPath: /tmp/kind-shared
      containerPath: /mnt/shared
networking:
  disableDefaultCNI: true
