apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: create-openchoreo-component
  title: Create OpenChoreo Component
  description: Create a new OpenChoreo component in a specified project and organization.
  tags:
    - openchoreo
spec:
  owner: guests
  type: component
  parameters:
    - title: Component Metadata
      required:
        - organization_name
        - project_name
        - component_name
        - component_type
      properties:
        component_name:
          title: Component Name
          type: string
          description: Name of the component
          ui:field: EntityNamePicker
        displayName:
          title: Display Name
          type: string
          description: Display name of the component
        description:
          title: Description
          type: string
          description: Help others understand what this component is for.
        project_name:
          title: Project Name
          type: string
          description: OpenChoreo project name
          ui:field: EntityPicker
          ui:options:
            catalogFilter:
              - kind: System
        organization_name:
          title: Organization Name
          type: string
          description: Help others understand what this project is for.
          ui:field: EntityPicker
          ui:options:
            catalogFilter:
              - kind: Domain
        component_type:
          title: Component Type
          type: string
          description: Describe the type of component you are creating
          enum:
            - Service
            - WebApplication
            - ScheduledTask
            - APIProxy
          enumNames:
            - 'Service is a long running process that exposes one or more APIs.'
            - 'Web Application is a user-facing application that runs in a web browser.'
            - 'Scheduled Task is a background job that runs on a schedule.'
            - 'API Proxy is a service that acts as a gateway to other APIs.'

    - title: CI Setup
      required:
        - useBuiltInCI
      properties:
        useBuiltInCI:
          title: Use Built-in CI in OpenChoreo
          description: OpenChoreo provides built-in CI capabilities for building components. Enable this to use the built-in CI.
          type: boolean
          ui:widget: radio
      
      dependencies:
        useBuiltInCI:
          allOf:
            - if:
                properties:
                  useBuiltInCI:
                    const: true
              then:
                properties:
                  repo_url:
                    title: Repository URL
                    type: string
                    description: GitHub repository URL (e.g., https://github.com/username/repository)
                    pattern: '^https://github\.com/[^/]+/[^/]+/?$'
                  branch:
                    title: Branch Name
                    type: string
                    description: The branch of the repository to use for the component.
                    default: main
                  component_path:
                    title: Component Path
                    type: string
                    description: The path within the repository where the component's source code is located.
                    default: .
                  # build_engine:
                  #   title: Build Engine
                  #   type: string
                  #   description: The engine to use for building the component
                  #   default: argo
                  #   enum:
                  #     - argo
                  build_template_name:
                    title: Argo Workflows Build Template Name
                    type: string
                    description: Name of the Argo Workflow build template to use
                    ui:field: BuildTemplatePicker
                  # build_parameters:
                  #   title: Build Parameters
                  #   type: array
                  #   description: Optional parameters to pass to the build template
                  #   items:
                  #     type: object
                  #     properties:
                  #       name: 
                  #         title: Parameter Name
                  #         type: string
                  #       value:
                  #         title: Parameter Value
                  #         type: string
                required:
                  - build_template_name
                  - repo_url
                  - branch
                  - component_path
            - if:
                properties:
                  useBuiltInCI:
                    const: false
              then:
                properties:
                  external_ci_note:
                    type: 'null'
                    ui:widget: markdown
                    description: |
                      ## Configure your CI

                      This section contains details for configuring your CI pipeline to notify OpenChoreo for each build.
  steps:
    - id: create-openchoreo-component
      name: Create OpenChoreo Component
      action: openchoreo:component:create
      input:
        orgName: ${{ parameters.organization_name }}
        projectName: ${{ parameters.project_name }}
        componentName: ${{ parameters.component_name }}
        displayName: ${{ parameters.displayName }}
        description: ${{ parameters.description }}
        componentType: ${{ parameters.component_type }}
        useBuiltInCI: ${{ parameters.useBuiltInCI }}
        repoUrl: ${{ parameters.repo_url }}
        branch: ${{ parameters.branch }}
        componentPath: ${{ parameters.component_path }}
        buildTemplateName: ${{ parameters.build_template_name }}
        # buildParameters: ${{ parameters.build_parameters }}
